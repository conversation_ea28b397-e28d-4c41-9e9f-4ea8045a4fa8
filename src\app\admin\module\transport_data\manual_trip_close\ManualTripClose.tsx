import React, { useEffect } from 'react';

const ManualTripClose: React.FC = () => {

    useEffect(() => {
        console.log('ManualTripClose component mounted');

        return () => {
            console.log('ManualTripClose component will unmount');
        };
    }, []);

    console.log('ManualTripClose component rendering');

    return (
        <h1>Manual Trip Close</h1>
    );
};

export default ManualTripClose;
