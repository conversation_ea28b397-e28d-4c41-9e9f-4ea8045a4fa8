import axios from 'axios'
import { UserResponse, RegisterResponse, BranchVendorRequest, BranchVendorResponse, UpdateUserRequest, UpdateUserResponse, PostRegisterRequest, PostRegisterResponse } from './UserModel'

const API_URL = import.meta.env.VITE_APP_API_URL;

export const USER_REGISTER_URL = `${API_URL}/organization/getregister`;
export const REGISTER_DATA_URL = `${API_URL}/organization/register`;
export const POST_REGISTER_URL = `${API_URL}/organization/postregister`;
export const BRANCH_VENDOR_URL = `${API_URL}/organization/branch_wise_vendor`;
export const UPDATE_USER_URL = `${API_URL}/organization/updateregister`;

export const getUserRegister = () => {
    return axios.get<UserResponse>(USER_REGISTER_URL);
};

export const getRegisterData = () => {
    return axios.get<RegisterResponse>(REGISTER_DATA_URL);
};

export const getBranchVendors = (params: BranchVendorRequest) => {
    return axios.post<BranchVendorResponse>(BRANCH_VENDOR_URL, params);
};

export const postRegister = (params: PostRegisterRequest) => {
    return axios.post<PostRegisterResponse>(POST_REGISTER_URL, params);
};

export const updateUser = (params: UpdateUserRequest) => {
    return axios.post<UpdateUserResponse>(UPDATE_USER_URL, params);
};