import React, { forwardRef, useImperativeHandle, useRef, useState } from 'react';
import { Modal } from 'bootstrap';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import ReactSelect from '../../../../Helper/ReactSelect';
import { toast } from 'react-toastify';
import { getRegisterData, getBranchVendors, postRegister } from './UserRequest';

interface ModalProps {
    id: string;
    title: string;
    onSuccess: () => void;
}

export interface AddUserModalRef {
    openModal: () => void;
    closeModal: () => void;
}

interface SelectOption {
    value: string;
    label: string;
}

interface FormValues {
    branch: SelectOption | null;
    vendor: SelectOption | null;
    userType: SelectOption | null;
    userName: string;
    email: string;
    password: string;
    confirmPassword: string;
}

const AddUserModal = forwardRef<AddUserModalRef, ModalProps>((props, ref) => {
    const { id, title, onSuccess } = props;
    const modalRef = useRef<HTMLDivElement>(null);
    const [loading, setLoading] = useState(false);
    const [branchOptions, setBranchOptions] = useState<{ value: string; label: string; }[]>([]);
    const [userTypeOptions, setUserTypeOptions] = useState<{ value: string; label: string; }[]>([]);
    const [vendorOptions, setVendorOptions] = useState<{ value: string; label: string; }[]>([]);

    const fetchRegisterData = async () => {
        try {
            setLoading(true);
            const response = await getRegisterData();

            if (response.data.success) {
                // Transform branch data
                const branchData = response.data.branch.map(branch => ({
                    value: branch.BRANCH_ID.toString(),
                    label: branch.BRANCH_NAME
                }));
                setBranchOptions(branchData);

                // Transform user type data
                const userTypeData = response.data.user_type.map(type => ({
                    value: type.REASON_ID.toString(),
                    label: type.REASON
                }));
                setUserTypeOptions(userTypeData);
            } else {
                throw new Error(response.data.message || 'Failed to fetch register data');
            }
        } catch (error) {
            console.error('Error fetching register data:', error);
            toast.error('Failed to load dropdown data. Please try again.');
        } finally {
            setLoading(false);
        }
    };

    const fetchVendors = async (branchId: number) => {
        try {
            setLoading(true);
            const response = await getBranchVendors({ branch_id: branchId });

            if (response.data.success) {
                const vendorData = response.data.vendor_list.map(vendor => ({
                    value: vendor.VENDOR_ID.toString(),
                    label: vendor.NAME
                }));
                setVendorOptions(vendorData);
                // Reset vendor selection in formik when branch changes
                formik.setFieldValue('vendor', null);
            } else {
                throw new Error(response.data.message || 'Failed to fetch vendor data');
            }
        } catch (error) {
            console.error('Error fetching vendors:', error);
            toast.error('Failed to load vendor data. Please try again.');
            setVendorOptions([]);
            formik.setFieldValue('vendor', null);
        } finally {
            setLoading(false);
        }
    };

    const validationSchema = Yup.object({
        branch: Yup.object().required('Branch is required'),
        vendor: Yup.object().required('Vendor is required'),
        userType: Yup.object().required('User Type is required'),
        userName: Yup.string().required('User Name is required'),
        email: Yup.string().email('Invalid email address').required('Email is required'),
        password: Yup.string()
            .min(6, 'Password must be at least 6 characters')
            .required('Password is required'),
        confirmPassword: Yup.string()
            .oneOf([Yup.ref('password')], 'Passwords must match')
            .required('Confirm Password is required')
    });

    const formik = useFormik<FormValues>({
        initialValues: {
            branch: null,
            vendor: null,
            userType: null,
            userName: '',
            email: '',
            password: '',
            confirmPassword: ''
        },
        validationSchema,
        onSubmit: async (values, { resetForm }) => {
            try {
                setLoading(true);

                // Prepare the request data according to the API specification
                const requestData = {
                    branch_id: parseInt(values.branch?.value || '0'),
                    vendor_id: parseInt(values.vendor?.value || '0'),
                    user_type: values.userType?.label || '',
                    name: values.userName,
                    email: values.email,
                    password: values.password,
                    password_confirmation: values.confirmPassword
                };

                console.log('Submitting registration data:', requestData);

                const response = await postRegister(requestData);

                if (response.data.success) {
                    toast.success(response.data.message || 'User registered successfully!');
                    resetForm();
                    onSuccess();
                    closeModal();
                } else {
                    throw new Error(response.data.message || 'Registration failed');
                }
            } catch (error: any) {
                console.error('Registration error:', error);

                // Handle validation errors specifically
                if (error.response?.data?.validation_error && error.response?.data?.data) {
                    const validationErrors = error.response.data.data;

                    // Create a more user-friendly field name mapping
                    const fieldNameMap: { [key: string]: string } = {
                        'email': 'Email',
                        'name': 'Name',
                        'password': 'Password',
                        'password_confirmation': 'Confirm Password',
                        'branch_id': 'Branch',
                        'vendor_id': 'Vendor',
                        'user_type': 'User Type'
                    };

                    // Show each validation error in a toast
                    Object.keys(validationErrors).forEach(field => {
                        const fieldErrors = validationErrors[field];
                        const friendlyFieldName = fieldNameMap[field] || field.charAt(0).toUpperCase() + field.slice(1);

                        if (Array.isArray(fieldErrors)) {
                            fieldErrors.forEach(errorMsg => {
                                toast.error(`${friendlyFieldName}: ${errorMsg}`);
                            });
                        }
                    });
                } else {
                    // Handle general errors
                    const errorMessage = error.response?.data?.message || error.message || 'Registration failed. Please try again.';
                    toast.error(errorMessage);
                }
            } finally {
                setLoading(false);
            }
        }
    });

    const handleBranchChange = async (value: SelectOption | null) => {
        formik.setFieldValue('branch', value);
        if (value?.value) {
            await fetchVendors(parseInt(value.value));
        } else {
            setVendorOptions([]);
            formik.setFieldValue('vendor', null);
        }
    };

    const openModal = () => {
        fetchRegisterData(); // Fetch data when modal opens
        const modalElement = document.getElementById(id);
        if (modalElement) {
            const modalInstance = Modal.getInstance(modalElement);
            if (modalInstance) {
                modalInstance.show();
            } else {
                new Modal(modalElement).show();
            }
        }
    };

    const closeModal = () => {
        const modalElement = document.getElementById(id);
        if (modalElement) {
            const modalInstance = Modal.getInstance(modalElement);
            if (modalInstance) {
                modalInstance.hide();
            }
        }
        formik.resetForm();
    };

    useImperativeHandle(ref, () => ({
        openModal,
        closeModal
    }));

    return (
        <div className="modal fade" tabIndex={-1} id={id} ref={modalRef}>
            <div className="modal-dialog modal-md" style={{ maxWidth: '500px' }}>
                <div className="modal-content">
                    <div className="modal-header">
                        <h5 className="modal-title">{title}</h5>
                        <button
                            type="button"
                            className="btn-close"
                            data-bs-dismiss="modal"
                            aria-label="Close"
                        ></button>
                    </div>
                    <form onSubmit={formik.handleSubmit} noValidate className="form">
                        <div className="modal-body" style={{ padding: '1.5rem' }}>
                            <div className="mb-4">
                                <label className="form-label required">Branch</label>
                                <ReactSelect
                                    options={branchOptions}
                                    value={formik.values.branch}
                                    onChange={handleBranchChange}
                                    disabled={loading}
                                    placeholder="Select Branch"
                                />
                                {formik.touched.branch && formik.errors.branch && (
                                    <div className="text-danger mt-1">{formik.errors.branch as string}</div>
                                )}
                            </div>

                            <div className="mb-4">
                                <label className="form-label required">Vendor</label>
                                <ReactSelect
                                    options={vendorOptions}
                                    value={formik.values.vendor}
                                    onChange={(value: SelectOption | null) => formik.setFieldValue('vendor', value)}
                                    disabled={loading || !formik.values.branch}
                                    placeholder="Select Vendor"
                                />
                                {formik.touched.vendor && formik.errors.vendor && (
                                    <div className="text-danger mt-1">{formik.errors.vendor as string}</div>
                                )}
                            </div>

                            <div className="mb-4">
                                <label className="form-label required">User Type</label>
                                <ReactSelect
                                    options={userTypeOptions}
                                    value={formik.values.userType}
                                    onChange={(value: SelectOption | null) => formik.setFieldValue('userType', value)}
                                    disabled={loading}
                                    placeholder="Select User Type"
                                />
                                {formik.touched.userType && formik.errors.userType && (
                                    <div className="text-danger mt-1">{formik.errors.userType as string}</div>
                                )}
                            </div>

                            <div className="mb-4">
                                <label className="form-label required">User Name</label>
                                <input
                                    type="text"
                                    className={`form-control ${formik.touched.userName && formik.errors.userName ? 'is-invalid' : ''}`}
                                    {...formik.getFieldProps('userName')}
                                    disabled={loading}
                                />
                                {formik.touched.userName && formik.errors.userName && (
                                    <div className="invalid-feedback">{formik.errors.userName}</div>
                                )}
                            </div>

                            <div className="mb-4">
                                <label className="form-label required">E-Mail Address</label>
                                <input
                                    type="email"
                                    className={`form-control ${formik.touched.email && formik.errors.email ? 'is-invalid' : ''}`}
                                    {...formik.getFieldProps('email')}
                                    disabled={loading}
                                />
                                {formik.touched.email && formik.errors.email && (
                                    <div className="invalid-feedback">{formik.errors.email}</div>
                                )}
                            </div>

                            <div className="mb-4">
                                <label className="form-label required">Password</label>
                                <input
                                    type="password"
                                    className={`form-control ${formik.touched.password && formik.errors.password ? 'is-invalid' : ''}`}
                                    {...formik.getFieldProps('password')}
                                    disabled={loading}
                                />
                                {formik.touched.password && formik.errors.password && (
                                    <div className="invalid-feedback">{formik.errors.password}</div>
                                )}
                            </div>

                            <div className="mb-4">
                                <label className="form-label required">Confirm Password</label>
                                <input
                                    type="password"
                                    className={`form-control ${formik.touched.confirmPassword && formik.errors.confirmPassword ? 'is-invalid' : ''}`}
                                    {...formik.getFieldProps('confirmPassword')}
                                    disabled={loading}
                                />
                                {formik.touched.confirmPassword && formik.errors.confirmPassword && (
                                    <div className="invalid-feedback">{formik.errors.confirmPassword}</div>
                                )}
                            </div>
                        </div>
                        <div className="modal-footer">
                            <button
                                type="button"
                                className="btn btn-light"
                                onClick={closeModal}
                                disabled={loading}
                            >
                                Cancel
                            </button>
                            <button
                                type="submit"
                                className="btn btn-primary"
                                disabled={loading || !formik.isValid}
                            >
                                {!loading && <span className="indicator-label">Register</span>}
                                {loading && (
                                    <span className='indicator-progress' style={{display: 'block'}}>
                                        Please wait...
                                        <span className='spinner-border spinner-border-sm align-middle ms-2'></span>
                                    </span>
                                )}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
});

export default AddUserModal; 