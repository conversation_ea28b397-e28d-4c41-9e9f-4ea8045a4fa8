export interface UserResponse {
    success: boolean;
    data: UserData[];
    message: string;
}

export interface UserData {
    name: string;
    id: number;
    BRANCH_ID: number;
    user_type: string;
    vendor_id: number;
    password: string;
    BRANCH_NAME: string;
}

export interface RegisterResponse {
    success: boolean;
    status: number;
    user_type: Array<{
        REASON: string;
        REASON_ID: number;
    }>;
    branch: Array<{
        BRANCH_ID: number;
        BRANCH_NAME: string;
    }>;
    message: string;
}

export interface BranchVendorRequest {
    branch_id: number;
}

export interface BranchVendorResponse {
    success: boolean;
    status: number;
    vendor_list: Array<{
        VENDOR_ID: number;
        NAME: string;
    }>;
    message: string;
}

export interface UpdateUserRequest {
    users_id: number;
    edit_password: string;
    edit_password_confirmation: string;
}

export interface UpdateUserResponse {
    success: boolean;
    status: number;
    message: string;
}

export interface PostRegisterRequest {
    branch_id: number;
    vendor_id: number;
    user_type: string;
    name: string;
    email: string;
    password: string;
    password_confirmation: string;
}

export interface PostRegisterResponse {
    success: boolean;
    status: number;
    message: string;
}